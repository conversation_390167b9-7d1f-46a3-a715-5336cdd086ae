package app.base.domain.question

import app.base.cutil.IdGenerator
import app.base.cutil.Meta
import app.base.cutil.Pos
import app.base.domain.account.modle.StatusEnum
import app.base.domain.question.modle.QuestionDetail
import app.base.domain.question.modle.QuestionDto
import app.base.domain.question.query.QQuestion
import app.base.domain.questionBank.query.QQuestionBank
import app.base.domain.questionFormat.query.QQuestionFormat
import app.base.domain.questionIndustries.query.QIndustry
import app.base.domain.questionLevel.query.QDifficultyLevel
import app.base.domain.questionType.query.QQuestionType
import app.base.domain.questionOption.query.QQuestionOption
import app.base.domain.questionFormat.QuestionFormat
import app.base.domain.questionIndustries.Industry
import app.base.domain.questionLevel.DifficultyLevel
import app.base.domain.questionType.QuestionType
import app.base.domain.questionOption.QuestionOption
import com.google.inject.Inject
import com.google.inject.Singleton
import mu.KotlinLogging
import cn.hutool.core.io.FileUtil
import cn.hutool.core.util.IdUtil
import cn.hutool.json.JSONUtil
import io.vertx.core.buffer.Buffer
import io.vertx.ext.web.RoutingContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream
import java.util.zip.ZipOutputStream


@Singleton
class QuestionManager @Inject constructor(
  private val idGenerator: IdGenerator,
  private val questionRepository: QuestionRepository
) {
  private val logger = KotlinLogging.logger { }

  suspend fun random(
    questionBankId: Long,
    count: Int,
  ): List<QuestionDetail> {
    return questionRepository.random(questionBankId, count)
  }

  suspend fun findWith(
    pos: Pos,
    title: String?,
    content: String?,
    industryId: Long?,
    questionTypeId: Long?,
    questionFormatId: Long?,
    difficultyId: Long?,
    questionBankId: Long?,
    status: StatusEnum?
  ): QuestionDto {
    val question = questionRepository.findWith(
      pos, title, content, industryId, questionTypeId,
      questionFormatId, difficultyId, questionBankId, status
    )
    val industries = QIndustry().findList()
    val type = QQuestionType().findList()
    val format = QQuestionFormat().findList()
    val level = QDifficultyLevel().findList()
    return QuestionDto(question, industries, type, format, level)
  }

  suspend fun create(
    title: String?,
    content: String,
    questionTypeId: Long,
    questionFormatId: Long,
    difficultyId: Long,
    questionBankId: Long,
    audioFile: String?,
    imageFile: String?,
    correctAnswer: String,
  ) {
    val qBank = QQuestionBank().id.eq(questionBankId).findOne() ?: throw Meta.notFound("QuestionBank", "该题库不存在")
    Question().apply {
      this.id = idGenerator.next()
      this.content = content
      this.industryId = qBank.industryId
      this.questionTypeId = questionTypeId
      this.questionFormatId = questionFormatId
      this.difficultyId = difficultyId
      this.questionBankId = questionBankId
      this.status = StatusEnum.ACTIVE
      this.correctAnswer = correctAnswer
      title?.let { this.title = it }
      audioFile?.let { this.audioFile = it }
      imageFile?.let { this.imageFile = it }
    }.save()
  }

  fun delete(id: Long) {
    QQuestion().id.eq(id).findOneOrEmpty().ifPresent { it.delete() }
  }

  fun update(
    id: Long,
    title: String?,
    content: String?,
    industryId: Long?,
    questionTypeId: Long?,
    questionFormatId: Long?,
    difficultyId: Long?,
    questionBankId: Long?,
    audioFile: String?,
    imageFile: String?,
    correctAnswer: String?,
    status: StatusEnum?
  ) {
    val obj = QQuestion().id.eq(id).findOne() ?: throw Meta.notFound("Question", "该题目不存在")
    title?.let { obj.title = it }
    content?.let { obj.content = it }
    industryId?.let { obj.industryId = it }
    questionTypeId?.let { obj.questionTypeId = it }
    questionFormatId?.let { obj.questionFormatId = it }
    difficultyId?.let { obj.difficultyId = it }
    questionBankId?.let { obj.questionBankId = it }
    audioFile?.let { obj.audioFile = it }
    imageFile?.let { obj.imageFile = it }
    correctAnswer?.let { obj.correctAnswer = it }
    status?.let { obj.status = it }
    obj.update()
  }


  suspend fun exportQuestions(
    ctx: RoutingContext
  ) {
    val exportData = exportAllQuestionData()
    val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))
    val fileName = "question_bank_export_${timestamp}.zip"

    // 创建临时目录
    val tempDir = File(System.getProperty("java.io.tmpdir"), "question_export_${IdUtil.fastSimpleUUID()}")
    tempDir.mkdirs()

    try {
      // 写入JSON数据文件
      val dataFile = File(tempDir, "question_data.json")
      FileUtil.writeUtf8String(JSONUtil.toJsonPrettyStr(exportData), dataFile)

      // 复制音频和图片文件，保持原始路径结构
      val mediaDir = File(tempDir, "media")
      mediaDir.mkdirs()

      exportData.questions.forEach { question ->
        if (question.audioFile.isNotEmpty()) {
          val audioFile = File(question.audioFile)
          if (audioFile.exists()) {
            // 保持原始路径结构
            val relativePath = if (question.audioFile.startsWith("/")) {
              question.audioFile.substring(1) // 移除开头的 /
            } else {
              question.audioFile
            }
            val targetFile = File(mediaDir, relativePath)
            targetFile.parentFile?.mkdirs() // 确保父目录存在
            FileUtil.copy(audioFile, targetFile, true)
          }
        }
        if (question.imageFile.isNotEmpty()) {
          val imageFile = File(question.imageFile)
          if (imageFile.exists()) {
            // 保持原始路径结构
            val relativePath = if (question.imageFile.startsWith("/")) {
              question.imageFile.substring(1) // 移除开头的 /
            } else {
              question.imageFile
            }
            val targetFile = File(mediaDir, relativePath)
            targetFile.parentFile?.mkdirs() // 确保父目录存在
            FileUtil.copy(imageFile, targetFile, true)
          }
        }
      }

      // 创建ZIP文件
      val zipFile = File(System.getProperty("java.io.tmpdir"), fileName)
      createZipFile(tempDir, zipFile)

      val response = ctx.response()
      // 设置响应头
      response.putHeader("Content-Type", "application/zip")
      response.putHeader("Content-Disposition", "attachment; filename=\"${fileName}\"")
      response.putHeader("Content-Length", zipFile.length().toString())

      // 发送文件
      val buffer = Buffer.buffer(FileUtil.readBytes(zipFile))
      response.end(buffer)

      // 清理临时文件
      FileUtil.del(zipFile)
    } finally {
      // 清理临时目录
      FileUtil.del(tempDir)
    }
  }

  suspend fun importQuestions(
    ctx: RoutingContext
  ) {
    val fileUploads = ctx.fileUploads()
    if (fileUploads.isEmpty()) {
      throw IllegalArgumentException("请上传题库压缩包文件")
    }

    val fileUpload = fileUploads.first()
    val zipFile = File(fileUpload.uploadedFileName())
    if (!zipFile.exists()) {
      throw IllegalArgumentException("上传的文件不存在")
    }

    // 验证文件类型
    if (!fileUpload.fileName().lowercase().endsWith(".zip")) {
      throw IllegalArgumentException("请上传ZIP格式的压缩包文件")
    }

    // 创建临时解压目录
    val tempDir = File(System.getProperty("java.io.tmpdir"), "question_import_${IdUtil.fastSimpleUUID()}")
    tempDir.mkdirs()

    try {
      // 解压ZIP文件
      extractZipFile(zipFile, tempDir)

      // 读取数据文件
      val dataFile = File(tempDir, "question_data.json")
      if (!dataFile.exists()) {
        throw IllegalArgumentException("压缩包中缺少数据文件 question_data.json")
      }

      val jsonContent = FileUtil.readUtf8String(dataFile)

      runCatching {
       val importData = JSONUtil.toBean(jsonContent, QuestionExportData::class.java)
        if (importData == null) throw Meta.failure("ImportQuestions", "题库导入失败: 数据文件解析失败")
        // 导入数据并处理媒体文件
        importQuestionData(importData, File(tempDir, "media"))
      }
    } catch (e: Exception) {
      throw Meta.failure("ImportQuestions", "题库导入失败: ${e.message}")
    } finally {
      FileUtil.del(tempDir)
    }
  }


  /**
   * 导出所有题库相关数据
   */
  fun exportAllQuestionData(): QuestionExportData {
    val questions = QQuestion().findList()
    val industries = QIndustry().findList()
    val questionTypes = QQuestionType().findList()
    val questionFormats = QQuestionFormat().findList()
    val difficultyLevels = QDifficultyLevel().findList()
    val questionOptions = QQuestionOption().findList()

    return QuestionExportData(
      questions = questions,
      industries = industries,
      questionTypes = questionTypes,
      questionFormats = questionFormats,
      difficultyLevels = difficultyLevels,
      questionOptions = questionOptions
    )
  }

  /**
   * 导入题库数据
   */
  suspend fun importQuestionData(importData: QuestionExportData, mediaDir: File) {
    // 导入行业数据
    importData.industries.forEach { industry ->
      Industry().apply {
        id = industry.id
        name = industry.name
        code = industry.code
        description = industry.description
        status = industry.status
      }.save()
    }

    // 导入题目类型数据
    importData.questionTypes.forEach { questionType ->
      QuestionType().apply {
        id = questionType.id
        name = questionType.name
        code = questionType.code
        description = questionType.description
        status = questionType.status
      }.save()
    }

    // 导入题型数据
    importData.questionFormats.forEach { questionFormat ->
      QuestionFormat().apply {
        id = questionFormat.id
        name = questionFormat.name
        code = questionFormat.code
        description = questionFormat.description
        status = questionFormat.status
      }.save()
    }

    // 导入难度等级数据
    importData.difficultyLevels.forEach { difficultyLevel ->
      DifficultyLevel().apply {
        id = difficultyLevel.id
        name = difficultyLevel.name
        code = difficultyLevel.code
        levelValue = difficultyLevel.levelValue
        description = difficultyLevel.description
        status = difficultyLevel.status
      }.save()
    }

    // 导入题目数据
    importData.questions.forEach { question ->
      Question().apply {
        id = question.id
        title = question.title
        content = question.content
        industryId = question.industryId
        questionTypeId = question.questionTypeId
        questionFormatId = question.questionFormatId
        difficultyId = question.difficultyId
        questionBankId = question.questionBankId
        correctAnswer = question.correctAnswer
        status = question.status

        // 处理音频文件 - 保持原始路径
        if (question.audioFile.isNotEmpty()) {
          val originalAudioPath = question.audioFile
          val relativePath = if (originalAudioPath.startsWith("/")) {
            originalAudioPath.substring(1) // 移除开头的 /
          } else {
            originalAudioPath
          }
          val sourceAudioFile = File(mediaDir, relativePath)
          if (sourceAudioFile.exists()) {
            val targetAudioFile = File(originalAudioPath)
            targetAudioFile.parentFile?.mkdirs() // 确保父目录存在
            FileUtil.copy(sourceAudioFile, targetAudioFile, true)
            audioFile = originalAudioPath // 保持原始路径
          }
        }

        // 处理图片文件 - 保持原始路径
        if (question.imageFile.isNotEmpty()) {
          val originalImagePath = question.imageFile
          val relativePath = if (originalImagePath.startsWith("/")) {
            originalImagePath.substring(1) // 移除开头的 /
          } else {
            originalImagePath
          }
          val sourceImageFile = File(mediaDir, relativePath)
          if (sourceImageFile.exists()) {
            val targetImageFile = File(originalImagePath)
            targetImageFile.parentFile?.mkdirs() // 确保父目录存在
            FileUtil.copy(sourceImageFile, targetImageFile, true)
            imageFile = originalImagePath // 保持原始路径
          }
        }
      }.save()
    }

    // 导入题目选项数据
    importData.questionOptions.forEach { questionOption ->
      QuestionOption().apply {
        id = questionOption.id
        questionId = questionOption.questionId
        optionKey = questionOption.optionKey
        optionText = questionOption.optionText
        sortOrder = questionOption.sortOrder
        status = questionOption.status
      }.save()
    }
  }


  private fun createZipFile(sourceDir: File, zipFile: File) {
    ZipOutputStream(FileOutputStream(zipFile)).use { zos ->
      sourceDir.walkTopDown().forEach { file ->
        if (file.isFile) {
          val relativePath = sourceDir.toPath().relativize(file.toPath()).toString()
          val entry = ZipEntry(relativePath.replace("\\", "/"))
          zos.putNextEntry(entry)
          FileInputStream(file).use { fis ->
            fis.copyTo(zos)
          }
          zos.closeEntry()
        }
      }
    }
  }

  private fun extractZipFile(zipFile: File, destDir: File) {
    ZipInputStream(FileInputStream(zipFile)).use { zis ->
      var entry = zis.nextEntry
      while (entry != null) {
        val file = File(destDir, entry.name)
        if (entry.isDirectory) {
          file.mkdirs()
        } else {
          file.parentFile?.mkdirs()
          FileOutputStream(file).use { fos ->
            zis.copyTo(fos)
          }
        }
        entry = zis.nextEntry
      }
    }
  }
}

/**
 * 题库导出数据结构
 */
data class QuestionExportData(
  val questions: List<Question>,
  val industries: List<Industry>,
  val questionTypes: List<QuestionType>,
  val questionFormats: List<QuestionFormat>,
  val difficultyLevels: List<DifficultyLevel>,
  val questionOptions: List<QuestionOption>
)
